/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as admin from "../admin.js";
import type * as adminHelpers from "../adminHelpers.js";
import type * as analytics from "../analytics.js";
import type * as announcements from "../announcements.js";
import type * as banking from "../banking.js";
import type * as crons from "../crons.js";
import type * as discord_commands from "../discord/commands.js";
import type * as discord_notifications from "../discord/notifications.js";
import type * as discord from "../discord.js";
import type * as guides from "../guides.js";
import type * as lib_permissions from "../lib/permissions.js";
import type * as lib_tornApi from "../lib/tornApi.js";
import type * as lib_validation from "../lib/validation.js";
import type * as lib_warCacheService from "../lib/warCacheService.js";
import type * as realtime from "../realtime.js";
import type * as targets from "../targets.js";
import type * as torn from "../torn.js";
import type * as users from "../users.js";
import type * as wars from "../wars.js";
import type * as warsAdvanced from "../warsAdvanced.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  adminHelpers: typeof adminHelpers;
  analytics: typeof analytics;
  announcements: typeof announcements;
  banking: typeof banking;
  crons: typeof crons;
  "discord/commands": typeof discord_commands;
  "discord/notifications": typeof discord_notifications;
  discord: typeof discord;
  guides: typeof guides;
  "lib/permissions": typeof lib_permissions;
  "lib/tornApi": typeof lib_tornApi;
  "lib/validation": typeof lib_validation;
  "lib/warCacheService": typeof lib_warCacheService;
  realtime: typeof realtime;
  targets: typeof targets;
  torn: typeof torn;
  users: typeof users;
  wars: typeof wars;
  warsAdvanced: typeof warsAdvanced;
}>;
declare const fullApiWithMounts: typeof fullApi;

export declare const api: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "internal">
>;

export declare const components: {};
