import { cronJobs } from "convex/server";
import { api } from "./_generated/api";

const crons = cronJobs();

// Transaction verification - runs every 10 minutes (same as old app)
crons.cron(
  "transaction-verification",
  "*/10 * * * *", // Every 10 minutes
  api.banking.runTransactionVerificationCron
);

// Withdrawal expiration - runs every hour at minute 0 (same as old app)
crons.cron(
  "withdrawal-expiration", 
  "0 * * * *", // Every hour at minute 0
  api.banking.runWithdrawalExpirationCron
);

export default crons;
