import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { requirePermission } from "./lib/permissions";
import { api } from "./_generated/api";

// Type definitions for Torn API responses
type TornUserResponse = {
  status?: { state?: string; until?: number };
  error?: { code?: string; error?: string };
  name?: string;
  level?: number;
  player_id?: number;
  profile_image?: string;
};

type TornWarResponse = {
  wars: {
    ranked?: {
      war_id: number;
      start: number;
      end: number | null;
      target: number;
      winner: number | null;
      factions: Array<{
        id: number;
        name: string;
        score: number;
        chain: number;
      }>;
    };
    raids: unknown[];
    territory: unknown[];
  };
  error?: { code: string; error: string };
};

type TornFactionMembersResponse = {
  members: Array<{
    id: number;
    name: string;
    level: number;
    days_in_faction: number;
    last_action: {
      status: string;
      timestamp: number;
      relative: string;
    };
    status: {
      description: string;
      details: string;
      state: string;
      until: number;
    };
    revive_setting: string;
    position: string;
    is_revivable: boolean;
    is_on_wall: boolean;
    is_in_oc: boolean;
    has_early_discharge: boolean;
  }>;
  error?: { code: string; error: string };
};

type TornChainResponse = {
  chain: {
    current: number;
    maximum: number;
    timeout: number;
    modifier: number;
    cooldown: number;
    start: number;
    end: number;
  };
  error?: { code: string; error: string };
};

// Target Lists Management

// Initialize shared target lists (admin function)
export const initializeSharedLists = mutation({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, "admin.view");

    const now = Date.now();
    
    // Check if shared lists already exist
    const existingSharedA = await ctx.db
      .query("targetLists")
      .withIndex("by_name", (q) => q.eq("name", "Faction Shared Targets A"))
      .first();
    
    const existingSharedB = await ctx.db
      .query("targetLists")
      .withIndex("by_name", (q) => q.eq("name", "Faction Shared Targets B"))
      .first();
    
    if (!existingSharedA) {
      await ctx.db.insert("targetLists", {
        name: "Faction Shared Targets A",
        userId: undefined,
        isShared: true,
        createdAt: now,
        updatedAt: now,
      });
    }
    
    if (!existingSharedB) {
      await ctx.db.insert("targetLists", {
        name: "Faction Shared Targets B",
        userId: undefined,
        isShared: true,
        createdAt: now,
        updatedAt: now,
      });
    }
    
    return { 
      message: "Shared target lists initialized successfully",
      createdA: !existingSharedA,
      createdB: !existingSharedB,
    };
  },
});

// Get all target lists (shared + user's custom list)
export const getLists = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    await requirePermission(ctx, "target.finder.view");

    // Get all shared lists (sorted by name to ensure consistent ordering)
    let sharedLists = await ctx.db
      .query("targetLists")
      .withIndex("by_shared", (q) => q.eq("isShared", true))
      .collect();

    // Auto-initialize shared lists if they don't exist (for convenience)
    if (sharedLists.length === 0) {
      const now = Date.now();
      
      // Create both shared lists
      const listAId = await ctx.db.insert("targetLists", {
        name: "Faction Shared Targets A",
        userId: undefined,
        isShared: true,
        createdAt: now,
        updatedAt: now,
      });
      
      const listBId = await ctx.db.insert("targetLists", {
        name: "Faction Shared Targets B",
        userId: undefined,
        isShared: true,
        createdAt: now,
        updatedAt: now,
      });
      
      // Fetch the created lists
      sharedLists = await ctx.db
        .query("targetLists")
        .withIndex("by_shared", (q) => q.eq("isShared", true))
        .collect();
    }

    // Sort shared lists to match original app ordering (A before B)
    sharedLists.sort((a, b) => a.name.localeCompare(b.name));

    // Get user's custom list
    const customList = await ctx.db
      .query("targetLists")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();

    // Start with shared lists at the top (matching original behavior)
    const lists = [
      ...sharedLists.map(list => ({
        id: list._id,
        name: list.name,
        userId: list.userId,
        isShared: list.isShared,
        isExternal: false,
      })),
    ];

    // Add custom list after shared lists
    if (customList) {
      lists.push({
        id: customList._id,
        name: "Custom List",
        userId: customList.userId,
        isShared: false,
        isExternal: false,
      });
    } else {
      // Placeholder for custom list
      lists.push({
        id: "custom-list-placeholder",
        name: "Custom List", 
        userId: user._id,
        isShared: false,
        isExternal: false,
      });
    }

    return lists;
  },
});

// Get targets for a specific list with real-time status
export const getTargets = action({
  args: { listName: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(api.users.getCurrentUser);
    if (!user) throw new Error("User not found");

    // Check cooldown (30 seconds)
    const TARGET_FINDER_COOLDOWN_MS = 30 * 1000;
    const now = Date.now();
    const lastFetch = user.lastTargetFinderFetch || 0;

    if (now - lastFetch < TARGET_FINDER_COOLDOWN_MS) {
      const remaining = Math.ceil((TARGET_FINDER_COOLDOWN_MS - (now - lastFetch)) / 1000);
      throw new Error(`Please wait ${remaining} seconds before fetching again.`);
    }

    // Update last fetch time
    await ctx.runMutation(api.users.updateLastTargetFinderFetch, { timestamp: now });

    // Find the target list
    let targetList;
    if (args.listName === "Custom List") {
      targetList = await ctx.runQuery(api.targets.getUserCustomList);
      if (!targetList) {
        targetList = await ctx.runMutation(api.targets.createOrGetCustomList);
      }
    } else {
      targetList = await ctx.runQuery(api.targets.getListByName, { name: args.listName });
    }

    if (!targetList) {
      throw new Error("Target list not found");
    }

    // Get targets from database
    const dbTargets = await ctx.runQuery(api.targets.getTargetsByListId, { 
      listId: targetList._id 
    });

    // Fetch fresh status for each target
    if (!user.tornApiKey || !user.tornApiKeyVerified) {
      throw new Error("Torn API key not configured or not verified");
    }

    const apiKey = user.tornApiKey; // In production, this would be decrypted
    const targetsWithStatus = [];

    // Fetch statuses with concurrency limit
    const CONCURRENCY_LIMIT = 10;
    let currentIndex = 0;

    const workers = Array.from({ length: CONCURRENCY_LIMIT }, async () => {
      while (currentIndex < dbTargets.length) {
        const index = currentIndex++;
        const target = dbTargets[index];
        
        try {
          const response = await fetch(
            `https://api.torn.com/user/${target.tornId}?selections=basic,profile&key=${apiKey}`
          );
          const data: TornUserResponse = await response.json();
          
          let status = "Unknown";
          let profilePicture: string | undefined;

          if (!data.error) {
            if (data.status?.state === "Hospital") {
              const remaining = (data.status.until || 0) - Date.now() / 1000;
              status = remaining > 0
                ? `Hospitalized (${Math.floor(remaining / 60)}m ${Math.floor(remaining % 60)}s)`
                : "Okay";
            } else {
              status = "Okay";
            }
            profilePicture = data.profile_image;
          } else {
            status = `Error (${data.error.code || data.error.error || "Unknown"})`;
          }

          targetsWithStatus.push({
            ...target,
            status,
            profilePicture,
            lastStatusFetch: now,
          });

          // Update target status in database
          await ctx.runMutation(api.targets.updateTargetStatus, {
            targetId: target._id,
            status,
            profilePicture,
            lastStatusFetch: now,
          });

        } catch (error) {
          targetsWithStatus.push({
            ...target,
            status: "Fetch Error",
            profilePicture: undefined,
            lastStatusFetch: now,
          });
        }
      }
    });

    await Promise.all(workers);

    // Sort targets: Okay first, then by hospital time remaining
    targetsWithStatus.sort((a, b) => {
      const aOkay = a.status === "Okay";
      const bOkay = b.status === "Okay";
      
      if (aOkay && !bOkay) return -1;
      if (!aOkay && bOkay) return 1;

      // Parse hospital time for sorting
      const parseTime = (status: string): number => {
        const match = status.match(/(\d+)m\s*(\d+)s/);
        if (!match) return Number.POSITIVE_INFINITY;
        return parseInt(match[1]) * 60 + parseInt(match[2]);
      };

      return parseTime(a.status) - parseTime(b.status);
    });

    return targetsWithStatus;
  },
});

// Helper queries
export const getListByName = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("targetLists")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();
  },
});

export const getUserCustomList = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) return null;

    return await ctx.db
      .query("targetLists")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();
  },
});

export const getTargetsByListId = query({
  args: { listId: v.id("targetLists") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("targets")
      .withIndex("by_list", (q) => q.eq("listId", args.listId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

// Create or get user's custom list
export const createOrGetCustomList = mutation({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    await requirePermission(ctx, "target.finder.view");

    // Check if user already has a custom list
    const existingList = await ctx.db
      .query("targetLists")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();

    if (existingList) return existingList;

    // Create new custom list
    const now = Date.now();
    return await ctx.db.insert("targetLists", {
      name: "Custom List",
      userId: user._id,
      isShared: false,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Create shared list (admin only)
export const createSharedList = mutation({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "target.finder.manage.shared_lists");

    // Check if list already exists
    const existing = await ctx.db
      .query("targetLists")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    if (existing) {
      throw new Error("A list with this name already exists");
    }

    const now = Date.now();
    return await ctx.db.insert("targetLists", {
      name: args.name,
      userId: undefined,
      isShared: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Add target to list
export const addTargetToList = mutation({
  args: {
    tornId: v.number(),
    listName: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    // Find the target list
    let targetList;
    if (args.listName === "Custom List") {
      await requirePermission(ctx, "target.finder.view");
      targetList = await ctx.db
        .query("targetLists")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .first();
      
      if (!targetList) {
        // Create custom list if it doesn't exist
        const now = Date.now();
        const listId = await ctx.db.insert("targetLists", {
          name: "Custom List",
          userId: user._id,
          isShared: false,
          createdAt: now,
          updatedAt: now,
        });
        targetList = { _id: listId, name: "Custom List", userId: user._id, isShared: false, createdAt: now, updatedAt: now };
      }
    } else {
      await requirePermission(ctx, "target.finder.manage.shared_lists");
      targetList = await ctx.db
        .query("targetLists")
        .withIndex("by_name", (q) => q.eq("name", args.listName))
        .first();
    }

    if (!targetList) {
      throw new Error("Target list not found");
    }

    // Check target limit (25 per list)
    const existingTargets = await ctx.db
      .query("targets")
      .withIndex("by_list", (q) => q.eq("listId", targetList._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    if (existingTargets.length >= 25) {
      throw new Error("This list already has the maximum of 25 targets");
    }

    // Check if target already exists in this list
    const existing = existingTargets.find(t => t.tornId === args.tornId);
    if (existing) {
      throw new Error("Target already exists in this list");
    }

    // For now, create target with basic info
    // In production, you'd fetch from Torn API to get name
    const now = Date.now();
    const targetId = await ctx.db.insert("targets", {
      listId: targetList._id,
      tornId: args.tornId,
      username: `Player ${args.tornId}`, // Would be fetched from API
      level: undefined,
      faction: undefined,
      status: "Unknown",
      profilePicture: undefined,
      respect: undefined,
      lastUpdated: now,
      lastStatusFetch: undefined,
      fairFight: undefined,
      battleStats: undefined,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    // Broadcast real-time update for shared lists only
    if (!targetList.userId) {
      // In a full implementation, this would broadcast to connected clients
      console.log(`[RealTime] Target ${args.tornId} added to shared list ${args.listName}`);
    }

    return { _id: targetId, listId: targetList._id, tornId: args.tornId, username: `Player ${args.tornId}`, createdAt: now, updatedAt: now };
  },
});

// Remove target from list
export const removeTargetFromList = mutation({
  args: {
    tornId: v.number(),
    listName: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    // Find the target list
    let targetList;
    if (args.listName === "Custom List") {
      await requirePermission(ctx, "target.finder.view");
      targetList = await ctx.db
        .query("targetLists")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .first();
    } else {
      await requirePermission(ctx, "target.finder.manage.shared_lists");
      targetList = await ctx.db
        .query("targetLists")
        .withIndex("by_name", (q) => q.eq("name", args.listName))
        .first();
    }

    if (!targetList) {
      throw new Error("Target list not found");
    }

    // Find and remove the target
    const target = await ctx.db
      .query("targets")
      .withIndex("by_list", (q) => q.eq("listId", targetList._id))
      .filter((q) => q.and(
        q.eq(q.field("tornId"), args.tornId),
        q.eq(q.field("isActive"), true)
      ))
      .first();

    if (!target) {
      throw new Error("Target not found in this list");
    }

    // Soft delete
    await ctx.db.patch(target._id, {
      isActive: false,
      updatedAt: Date.now(),
    });

    // Broadcast real-time update for shared lists only
    if (!targetList.userId) {
      console.log(`[RealTime] Target ${args.tornId} removed from shared list ${args.listName}`);
    }

    return { success: true };
  },
});

// Update target status
export const updateTargetStatus = mutation({
  args: {
    targetId: v.id("targets"),
    status: v.string(),
    profilePicture: v.optional(v.string()),
    lastStatusFetch: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.targetId, {
      status: args.status,
      profilePicture: args.profilePicture,
      lastStatusFetch: args.lastStatusFetch,
      lastUpdated: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Get cooldown information
export const getTargetFinderCooldown = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) throw new Error("User not found");

    const TARGET_FINDER_COOLDOWN_MS = 30 * 1000;
    const now = Date.now();
    const lastFetch = user.lastTargetFinderFetch || 0;
    
    let remaining = 0;
    if (now - lastFetch < TARGET_FINDER_COOLDOWN_MS) {
      remaining = Math.ceil((TARGET_FINDER_COOLDOWN_MS - (now - lastFetch)) / 1000);
    }

    return { remaining };
  },
});

// War system integration
export const getWarInfo = action({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(api.users.getCurrentUser);
    if (!user?.tornApiKey) {
      throw new Error("Torn API key not configured");
    }

    const response = await fetch(`https://api.torn.com/v2/faction/wars?key=${user.tornApiKey}`);
    const data: TornWarResponse = await response.json();

    if (data.error) {
      throw new Error(`Torn API Error: ${data.error.error || data.error.code}`);
    }

    return data;
  },
});

export const getEnemyFactionMembers = action({
  args: { factionId: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(api.users.getCurrentUser);
    if (!user?.tornApiKey) {
      throw new Error("Torn API key not configured");
    }

    const response = await fetch(
      `https://api.torn.com/v2/faction/${args.factionId}/members?striptags=true&key=${user.tornApiKey}`
    );
    const data: TornFactionMembersResponse = await response.json();

    if (data.error) {
      throw new Error(`Torn API Error: ${data.error.error || data.error.code}`);
    }

    // Transform to target format
    const now = Math.floor(Date.now() / 1000);
    return data.members.map((member) => {
      let status = "Okay";

      if (member.status?.state === "Hospital") {
        const remainingSeconds = member.status.until - now;
        if (remainingSeconds > 0) {
          const minutes = Math.floor(remainingSeconds / 60);
          const seconds = remainingSeconds % 60;
          status = `Hospitalized (${minutes}m ${seconds}s)`;
        }
      }

      return {
        _id: `enemy-${member.id}`,
        listId: `enemy-faction-${args.factionId}`,
        tornId: member.id,
        username: member.name,
        level: member.level,
        status,
        profilePicture: undefined,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        lastAction: member.last_action,
        position: member.position,
        isRevivable: member.is_revivable,
        isOnWall: member.is_on_wall,
        isInOc: member.is_in_oc,
        hasEarlyDischarge: member.has_early_discharge,
      };
    });
  },
});

export const getChainInfo = action({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(api.users.getCurrentUser);
    if (!user?.tornApiKey) {
      throw new Error("Torn API key not configured");
    }

    const response = await fetch(`https://api.torn.com/v2/faction/chain?key=${user.tornApiKey}`);
    const data: TornChainResponse = await response.json();

    if (data.error) {
      throw new Error(`Torn API Error: ${data.error.error || data.error.code}`);
    }

    return data;
  },
});

// Get single target status (for real-time updates)
export const getSingleTargetStatus = action({
  args: { tornId: v.number() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.runQuery(api.users.getCurrentUser);
    if (!user?.tornApiKey) {
      throw new Error("Torn API key not configured");
    }

    try {
      const response = await fetch(
        `https://api.torn.com/user/${args.tornId}?selections=basic,profile&key=${user.tornApiKey}`
      );
      const data: TornUserResponse = await response.json();

      let status = "Unknown";
      let profilePicture: string | undefined;

      if (!data.error) {
        if (data.status?.state === "Hospital") {
          const remaining = (data.status.until || 0) - Date.now() / 1000;
          status = remaining > 0
            ? `Hospitalized (${Math.floor(remaining / 60)}m ${Math.floor(remaining % 60)}s)`
            : "Okay";
        } else {
          status = "Okay";
        }
        profilePicture = data.profile_image;
      } else {
        status = `Error (${data.error.code || data.error.error || "Unknown"})`;
      }

      return {
        tornId: args.tornId,
        status,
        profilePicture,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        tornId: args.tornId,
        status: "Fetch Error",
        profilePicture: undefined,
        timestamp: new Date().toISOString(),
      };
    }
  },
});

// Legacy functions for backward compatibility
export const getTargets_old = query({
  args: {
    levelMin: v.optional(v.number()),
    levelMax: v.optional(v.number()),
    respectMin: v.optional(v.number()),
    respectMax: v.optional(v.number()),
    fairFightMin: v.optional(v.number()),
    fairFightMax: v.optional(v.number()),
    status: v.optional(v.string()),
    faction: v.optional(v.string()),
    search: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // This maintains backward compatibility with old filtering system
    let query = ctx.db.query("targets")
      .filter((q) => q.eq(q.field("isActive"), true));

    if (args.levelMin !== undefined) {
      query = query.filter((q) => q.gte(q.field("level"), args.levelMin!));
    }
    if (args.levelMax !== undefined) {
      query = query.filter((q) => q.lte(q.field("level"), args.levelMax!));
    }

    let targets = await query.take(args.limit ?? 100);

    if (args.search) {
      const searchTerm = args.search.toLowerCase();
      targets = targets.filter(target => 
        target.username.toLowerCase().includes(searchTerm) ||
        target.tornId.toString().includes(searchTerm)
      );
    }

    return targets;
  },
});