import React, { useState, useMemo, useCallback } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TargetCard } from './TargetCard';
import { TargetFilters } from './TargetFilters';
import { TargetStats } from './TargetStats';
import { ErrorBoundary } from './ErrorBoundary';
import { usePermissions } from '../../hooks/usePermissions';
import { AddTargetDialog } from './AddTargetDialog';
import { ChainStatusCard } from './ChainStatusCard';
import { useRealTimeTargets } from '../../hooks/useRealTimeTargets';
import { useRealTimeChain } from '../../hooks/useRealTimeChain';
import { useRealTimeCooldown } from '../../hooks/useRealTimeCooldown';

type StatusFilter = "all" | "okay" | "hospitalized" | "error";
type SortBy = "smart" | "name" | "tornId" | "status" | "added";
type SortOrder = "asc" | "desc";
type ViewMode = "grid" | "list";

export function TargetFinder() {
  const { canViewTargets } = usePermissions();
  const [selectedList, setSelectedList] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");
  const [sortBy] = useState<SortBy>("smart");
  const [sortOrder] = useState<SortOrder>("asc");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | undefined>();

  // Fetch lists
  const lists = useQuery(api.targets.getLists) ?? [];
  const listsLoading = lists === undefined;

  // Fetch targets for selected list
  const getTargetsAction = useAction(api.targets.getTargets);
  const [targetsData, setTargetsData] = useState<any[]>([]);
  const [targetsLoading, setTargetsLoading] = useState(false);
  const [targetsError, setTargetsError] = useState<string | null>(null);

  // Fetch war info
  const getWarInfoAction = useAction(api.targets.getWarInfo);
  const [warInfo, setWarInfo] = useState<any>(null);

  // Fetch chain info
  const getChainInfoAction = useAction(api.targets.getChainInfo);
  const [chainInfo, setChainInfo] = useState<any>(null);

  // Fetch enemy faction members
  const getEnemyMembersAction = useAction(api.targets.getEnemyFactionMembers);
  const [enemyMembersData, setEnemyMembersData] = useState<any[]>([]);
  const [enemyMembersLoading, setEnemyMembersLoading] = useState(false);

  // Real-time hooks
  const { 
    remainingTime: cooldownRemaining, 
    isOnCooldown, 
    formatRemainingTime: formatCooldownTime 
  } = useRealTimeCooldown({ feature: 'targetFinder' });
  
  const {
    chainRemaining: realTimeChainRemaining,
    chainStatus,
    chainInfo: realTimeChainInfo,
    formatRemainingTime: formatChainTime
  } = useRealTimeChain();
  
  const {
    updateTargetStatuses,
    getTargetCountdown,
    isTargetReady
  } = useRealTimeTargets();

  // Get cooldown info
  const cooldownData = useQuery(api.targets.getTargetFinderCooldown);

  // Remove target mutations
  const removeTargetFromList = useMutation(api.targets.removeTargetFromList);

  // Determine if current list is an enemy faction list
  const isEnemyFactionList = selectedList.startsWith("Enemy Faction:");

  // Extract enemy faction ID from war information
  const enemyFactionId = useMemo(() => {
    if (!warInfo?.wars?.ranked) return null;
    const ourFactionId = 53100;
    const enemyFaction = warInfo.wars.ranked.factions.find(
      (faction: any) => faction.id !== ourFactionId,
    );
    return enemyFaction ? String(enemyFaction.id) : null;
  }, [warInfo]);

  // Use appropriate data source based on list type with real-time status updates
  const targets = useMemo(() => {
    const baseTargets = isEnemyFactionList ? (enemyMembersData || []) : (targetsData || []);
    
    // Apply real-time status updates to hospitalized targets
    return updateTargetStatuses(baseTargets);
  }, [isEnemyFactionList, enemyMembersData, targetsData, updateTargetStatuses]);

  // Calculate chain remaining time (use real-time version if available, fallback to manual calculation)
  const chainRemaining = useMemo(() => {
    if (realTimeChainRemaining !== null) {
      return realTimeChainRemaining;
    }
    if (!chainInfo?.chain) return null;
    const now = Date.now() / 1000;
    const remaining = chainInfo.chain.timeout - now;
    return remaining > 0 ? remaining : 0;
  }, [realTimeChainRemaining, chainInfo]);
  
  // Use real-time chain info if available
  const effectiveChainInfo = realTimeChainInfo || chainInfo;

  // Filtered and sorted targets
  const filteredTargets = useMemo(() => {
    let filtered = targets;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (target: any) =>
          target.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          target.tornId.toString().includes(searchTerm),
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((target: any) => {
        const status = target.status;
        switch (statusFilter) {
          case "okay":
            return status === "Okay";
          case "hospitalized":
            return status.includes("Hospitalized");
          case "error":
            return status.includes("Error") || status === "Fetch Error";
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filtered.sort((a: any, b: any) => {
      if (sortBy === "smart") {
        // Smart sorting: Okay first, then by hospital time
        const getStatusPriority = (status: string): number => {
          if (status === "Okay") return 1;
          if (status.includes("Hospitalized")) return 2;
          return 3;
        };

        const aPriority = getStatusPriority(a.status);
        const bPriority = getStatusPriority(b.status);

        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }

        if (aPriority === 1) {
          return a.username.toLowerCase().localeCompare(b.username.toLowerCase());
        }
        if (aPriority === 2) {
          const getHospitalTime = (status: string): number => {
            const match = status.match(/(\d+)m\s*(\d+)s/);
            if (!match) return Number.POSITIVE_INFINITY;
            return parseInt(match[1]) * 60 + parseInt(match[2]);
          };
          const aTime = getHospitalTime(a.status);
          const bTime = getHospitalTime(b.status);
          if (aTime !== bTime) {
            return aTime - bTime;
          }
          return a.username.toLowerCase().localeCompare(b.username.toLowerCase());
        }
        return a.username.toLowerCase().localeCompare(b.username.toLowerCase());
      }

      // Other sorting modes would go here
      return 0;
    });

    return filtered;
  }, [targets, searchTerm, statusFilter, sortBy, sortOrder]);

  // Group targets by status for filter buttons
  const targetStats = useMemo(() => {
    if (!targets.length) return { all: 0, okay: 0, hospitalized: 0, error: 0 };

    const stats = { all: targets.length, okay: 0, hospitalized: 0, error: 0 };
    stats.okay = targets.filter((t: any) => t.status === "Okay").length;
    stats.hospitalized = targets.filter((t: any) =>
      t.status.includes("Hospitalized"),
    ).length;
    stats.error = targets.filter((t: any) => {
      const status = t.status;
      return status.includes("Error") || status === "Fetch Error";
    }).length;

    return stats;
  }, [targets]);

  // Load war and chain info on component mount
  React.useEffect(() => {
    const loadWarInfo = async () => {
      try {
        const data = await getWarInfoAction({});
        setWarInfo(data);
      } catch (error) {
        console.log("War info not available:", error);
      }
    };

    const loadChainInfo = async () => {
      try {
        const data = await getChainInfoAction({});
        setChainInfo(data);
      } catch (error) {
        console.log("Chain info not available:", error);
      }
    };

    loadWarInfo();
    loadChainInfo();

    // Refresh chain info every 15 seconds
    const chainInterval = setInterval(loadChainInfo, 15000);
    return () => clearInterval(chainInterval);
  }, [getWarInfoAction, getChainInfoAction]);

  // Handle list selection
  const handleListSelect = useCallback(async (listName: string) => {
    setSelectedList(listName);
    setTargetsData([]);
    setTargetsError(null);

    if (!listName) return;

    if (listName.startsWith("Enemy Faction:")) {
      // Handle enemy faction lists
      if (enemyFactionId) {
        setEnemyMembersLoading(true);
        try {
          const data = await getEnemyMembersAction({ factionId: enemyFactionId });
          setEnemyMembersData(data);
          setLastRefreshTime(new Date());
        } catch (error: any) {
          setTargetsError(error.message);
        } finally {
          setEnemyMembersLoading(false);
        }
      }
    } else {
      // Handle regular target lists
      setTargetsLoading(true);
      try {
        const data = await getTargetsAction({ listName });
        setTargetsData(data);
        setLastRefreshTime(new Date());
      } catch (error: any) {
        setTargetsError(error.message);
      } finally {
        setTargetsLoading(false);
      }
    }
  }, [getTargetsAction, getEnemyMembersAction, enemyFactionId]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (!selectedList) return;
    await handleListSelect(selectedList);
  }, [selectedList, handleListSelect]);

  // Handle target removal
  const handleRemoveTarget = async (tornId: number) => {
    if (!selectedList) return;

    try {
      await removeTargetFromList({
        tornId,
        listName: selectedList,
      });
      // Refresh the list
      await handleListSelect(selectedList);
    } catch (error: any) {
      console.error("Failed to remove target:", error);
    }
  };

  // Handle target addition
  const handleAddTarget = async () => {
    // Refresh the list after adding
    await handleListSelect(selectedList);
  };

  if (!canViewTargets()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to view targets.</p>
        </div>
      </div>
    );
  }

  const isLoading = targetsLoading || enemyMembersLoading;
  const isRefreshing = isLoading;

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🎯 Target Finder</h1>
          <p className="text-gray-600">Find and track targets with real-time status updates</p>
        </div>
        <div className="flex items-center space-x-4">
          <AddTargetDialog
            selectedList={selectedList}
            onSuccess={handleAddTarget}
          />
          <button
            onClick={handleRefresh}
            disabled={!selectedList || isRefreshing}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </button>
        </div>
      </div>

      {/* Last refresh time */}
      {lastRefreshTime && (
        <div className="text-sm text-gray-500">
          Last updated: {lastRefreshTime.toLocaleTimeString()}
        </div>
      )}

      {/* Chain Status Card with Real-time Updates */}
      {effectiveChainInfo && chainRemaining !== null && chainRemaining > 0 && (
        <div className="relative">
          <ChainStatusCard
            chainInfo={effectiveChainInfo}
            chainRemaining={chainRemaining}
            chainStatus={chainStatus}
          />
          {/* Real-time chain countdown overlay */}
          <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm font-mono">
            ⏱️ {formatChainTime()}
          </div>
        </div>
      )}

      {/* Filters */}
      <ErrorBoundary>
        <TargetFilters
          selectedList={selectedList}
          setSelectedList={handleListSelect}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          viewMode={viewMode}
          setViewMode={setViewMode}
          lists={lists}
          listsLoading={listsLoading}
          warInfo={warInfo}
          enemyFactionId={enemyFactionId}
          cooldownData={cooldownData}
          cooldownRemaining={cooldownRemaining}
          isOnCooldown={isOnCooldown}
          formatCooldownTime={formatCooldownTime}
          onRefresh={handleRefresh}
          isRefreshing={isRefreshing}
          targetStats={targetStats}
        />
      </ErrorBoundary>

      {/* Targets Grid/List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : targetsError ? (
        <div className="text-center py-12">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading targets</h3>
          <p className="text-red-600 mb-4">{targetsError}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      ) : !selectedList ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🎯</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Select a target list
          </h3>
          <p className="text-gray-600">
            Choose a target list from the dropdown above to view targets.
          </p>
        </div>
      ) : filteredTargets.length > 0 ? (
        <div className={viewMode === "grid" 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          : "space-y-4"
        }>
          {filteredTargets.map((target: any) => (
            <ErrorBoundary key={target._id || target.tornId}>
              <TargetCard 
                target={target}
                onRemove={() => handleRemoveTarget(target.tornId)}
                canRemove={selectedList !== "" && !isEnemyFactionList}
                viewMode={viewMode}
                countdown={getTargetCountdown(target)}
                isReady={isTargetReady(target)}
              />
            </ErrorBoundary>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🎯</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No targets found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== "all"
              ? "Try adjusting your filters to find more targets."
              : "No targets have been added to this list yet."
            }
          </p>
          {(searchTerm || statusFilter !== "all") && (
            <button
              onClick={() => {
                setSearchTerm("");
                setStatusFilter("all");
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Clear Filters
            </button>
          )}
        </div>
      )}
    </div>
  );
}