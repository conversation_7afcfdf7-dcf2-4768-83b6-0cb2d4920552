import { useSession } from './useSession';

export function usePermissions() {
  const { convexUser } = useSession();
  const permissions = convexUser?.permissions || [];

  console.log('🔑 usePermissions - State:', {
    hasUser: !!convexUser,
    permissions,
    permissionCount: permissions.length,
    specificPermissions: permissions
  });

  function hasPermission(permission: string) {
    const result = permissions.includes(permission);
    console.log(`🔑 usePermissions - Checking permission '${permission}':`, result);
    return result;
  }

  // Banking-specific permission helpers
  const canAccessBanking = () => {
    const result = hasPermission('banking.view') || 
           hasPermission('banking.requests.manage') || 
           hasPermission('admin.view');
    console.log('🔑 usePermissions - canAccessBanking:', result);
    return result;
  };

  const canManageBanking = () => {
    return hasPermission('banking.requests.manage') || 
           hasPermission('admin.view');
  };

  const isAdmin = () => {
    return hasPermission('admin.view');
  };

  // Announcements-specific permission helpers
  const canCreateAnnouncements = () => {
    return hasPermission('announcements.manage') || 
           hasPermission('admin.view');
  };

  const canEditAnnouncements = () => {
    return hasPermission('announcements.manage') || 
           hasPermission('admin.view');
  };

  const canDeleteAnnouncements = () => {
    return hasPermission('announcements.manage') || 
           hasPermission('admin.view');
  };

  const canManageAnnouncements = () => {
    return hasPermission('announcements.manage') || 
           hasPermission('admin.view');
  };

  // Targets-specific permission helpers
  const canViewTargets = () => {
    return hasPermission('target.finder.view') || 
           hasPermission('target.finder.manage.shared_lists') || 
           hasPermission('admin.view');
  };

  const canUpdateTargets = () => {
    return hasPermission('target.finder.manage.shared_lists') || 
           hasPermission('admin.view');
  };

  const canDeleteTargets = () => {
    return hasPermission('target.finder.manage.shared_lists') || 
           hasPermission('admin.view');
  };

  const canManageTargets = () => {
    return hasPermission('target.finder.manage.shared_lists') || 
           hasPermission('admin.view');
  };

  // Wars-specific permission helpers
  const canViewWars = () => {
    return hasPermission('wars.view') || 
           hasPermission('admin.view');
  };

  const canCreateWars = () => {
    return hasPermission('admin.view');
  };

  const canEditWars = () => {
    return hasPermission('admin.view');
  };

  const canUpdateWars = () => {
    return hasPermission('admin.view');
  };

  const canDeleteWars = () => {
    return hasPermission('admin.view');
  };

  const canManageWars = () => {
    return hasPermission('admin.view');
  };

  // Role hierarchy helpers
  const hasMinimumRole = (requiredRole: string) => {
    if (!convexUser?.roleLevel) return false;
    return getRoleLevel(requiredRole) <= convexUser.roleLevel;
  };

  const getRoleLevel = (roleName: string): number => {
    const roleLevels: Record<string, number> = {
      'recruit': 1,
      'chimpanzee': 2,
      'orangutan': 3,
      'baboon': 4,
      'primate-liaison': 5,
      'gorilla': 6,
      'monkey-mentor': 7,
      'co-leader': 8,
      'leader': 9,
      'system-admin': 10,
    };
    
    return roleLevels[roleName.toLowerCase()] || 0;
  };

  const canManageUser = (targetUser: { roleLevel?: number }) => {
    if (!convexUser?.roleLevel || !targetUser.roleLevel) return false;
    return convexUser.roleLevel > targetUser.roleLevel;
  };

  // Guides-specific permission helpers
  const canViewGuides = () => {
    return hasPermission('guides.view') || 
           hasPermission('guides.manage') || 
           hasPermission('admin.view');
  };

  const canCreateGuides = () => {
    return hasPermission('guides.manage') || 
           hasPermission('admin.view');
  };

  const canEditGuides = () => {
    return hasPermission('guides.manage') || 
           hasPermission('admin.view');
  };

  const canDeleteGuides = () => {
    return hasPermission('guides.manage') || 
           hasPermission('admin.view');
  };

  const canManageGuides = () => {
    return hasPermission('guides.manage') || 
           hasPermission('admin.view');
  };

  return {
    permissions,
    hasPermission,
    hasMinimumRole,
    canManageUser,
    canAccessBanking,
    canManageBanking,
    canCreateAnnouncements,
    canEditAnnouncements,
    canDeleteAnnouncements,
    canManageAnnouncements,
    canViewTargets,
    canUpdateTargets,
    canDeleteTargets,
    canManageTargets,
    canViewWars,
    canCreateWars,
    canEditWars,
    canUpdateWars,
    canDeleteWars,
    canManageWars,
    canViewGuides,
    canCreateGuides,
    canEditGuides,
    canDeleteGuides,
    canManageGuides,
    isAdmin,
  };
} 