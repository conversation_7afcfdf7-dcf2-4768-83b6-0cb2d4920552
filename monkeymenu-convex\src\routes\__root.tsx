import { createRootRoute, Outlet, Link } from '@tanstack/react-router';
import { SignedIn, SignedOut, UserButton } from '@clerk/clerk-react';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';
import { useSession } from '../hooks/useSession';
import { usePermissions } from '../hooks/usePermissions';
import { OnboardingGuard } from '../components/auth/OnboardingGuard';
import { PermissionMigration } from '../components/auth/PermissionMigration';

export const Route = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  const { convexUser } = useSession();
  const { hasPermission, hasMinimumRole } = usePermissions();
  // Allow admin access exactly like the original app
  const canViewAdmin = hasPermission('admin.view') || 
                       hasMinimumRole('monkey-mentor') ||
                       convexUser?.role === 'Monkey Mentor' ||
                       convexUser?.role === 'Co-Leader' ||
                       convexUser?.role === 'Leader';
  
  // Debug logging for permissions
  console.log('🔍 Permission Debug:', {
    hasUser: !!convexUser,
    permissions: convexUser?.permissions,
    roleLevel: convexUser?.roleLevel,
    role: convexUser?.role,
    factionPosition: convexUser?.role, // role field contains faction position
    hasAdminView: hasPermission('admin.view'),
    hasMonkeyMentor: hasMinimumRole('monkey-mentor'),
    canViewAdmin,
    // Check if permissions match expected permissions
    hasGuidesView: hasPermission('guides.view'),
    hasGuidesManage: hasPermission('guides.manage'),
    hasAnnouncementsView: hasPermission('announcements.view'),
    hasAnnouncementsManage: hasPermission('announcements.manage'),
    hasTargetFinderView: hasPermission('target.finder.view'),
    hasBankingView: hasPermission('banking.view'),
    hasWarsView: hasPermission('wars.view'),
    hasDashboardView: hasPermission('dashboard.view'),
    permissionsDetail: convexUser?.permissions
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="text-xl font-bold text-gray-900">
                MonkeyMenu
              </Link>
              <SignedIn>
                <div className="ml-8 flex space-x-4">
                  {hasPermission('dashboard.view') && (
                    <Link
                      to="/dashboard"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Dashboard
                    </Link>
                  )}
                  {hasPermission('banking.view') && (
                    <Link
                      to="/banking"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Banking
                    </Link>
                  )}
                  {hasPermission('announcements.view') && (
                    <Link
                      to="/announcements"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Announcements
                    </Link>
                  )}
                  {hasPermission('target.finder.view') ? (
                    <Link
                      to="/targets"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Targets
                    </Link>
                  ) : (
                    <span className="text-red-500 px-3 py-2 text-sm font-medium" title="Missing target.finder.view permission">
                      Targets (No Permission)
                    </span>
                  )}
                  {hasPermission('wars.view') && (
                    <Link
                      to="/wars"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Wars
                    </Link>
                  )}
                  {hasPermission('guides.view') && (
                    <Link
                      to="/guides"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Guides
                    </Link>
                  )}
                  {hasPermission('wars.view') && (
                    <Link
                      to="/analytics"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Analytics
                    </Link>
                  )}
                  {canViewAdmin && (
                    <Link
                      to="/admin"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Admin
                    </Link>
                  )}
                </div>
              </SignedIn>
            </div>
            <div className="flex items-center">
              <SignedIn>
                <UserButton />
              </SignedIn>
              <SignedOut>
                <Link
                  to="/sign-in"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Sign In
                </Link>
              </SignedOut>
            </div>
          </div>
        </div>
      </nav>
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <SignedIn>
          <PermissionMigration />
          <OnboardingGuard>
            <Outlet />
          </OnboardingGuard>
        </SignedIn>
        <SignedOut>
          <Outlet />
        </SignedOut>
      </main>
      <TanStackRouterDevtools />
    </div>
  );
}